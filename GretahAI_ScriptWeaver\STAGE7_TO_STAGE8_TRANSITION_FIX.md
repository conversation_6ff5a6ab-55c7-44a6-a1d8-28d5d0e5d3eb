# Stage 7 → Stage 8 Transition Fix

## Problem Summary

The Stage 7 to Stage 8 transition was not working correctly. After completing all test case steps and clicking the "Run Script" button in Stage 7, users were not seeing the Stage 8 transition options. The log output showed:

```
End of run_app: step_index=3, total_steps=4, ready_for_script=True
```

This indicated that all steps were completed (step_index=3 out of total_steps=4), but the Stage 8 transition was not triggering.

## Root Cause Analysis

The issue was in `stages/stage7.py`. The Stage 8 transition UI was **nested inside the "Run Test Script" button click handler**, making it only visible momentarily during button processing and disappearing after `st.rerun()`.

**Problematic Code Structure:**
```python
if st.button("Run Test Script"):
    # ... test execution logic ...
    if next_step_index < state.total_steps:
        # Advance to next step
    else:
        # Stage 8 transition UI was HERE - inside button handler!
        # This made it non-persistent and invisible to users
```

## Solution Implemented

### 1. **Moved Completion Check to Top Level**
Added a check for `state.all_steps_done` at the beginning of `stage7_run_script()` function:

```python
def stage7_run_script(state):
    # Check if all steps are completed BEFORE showing the run button
    if hasattr(state, 'all_steps_done') and state.all_steps_done:
        # Show Stage 8 transition UI persistently
        # ... transition options ...
        return  # Early return to prevent showing run button
```

### 2. **Made Stage 8 Transition UI Persistent**
The transition options now appear whenever `all_steps_done=True`, regardless of button clicks.

### 3. **Simplified Completion Logic**
When the last step completes, the button handler now:
```python
else:
    # All steps processed - set flag and force rerun
    state.all_steps_done = True
    st.session_state['state'] = state
    st.rerun()  # This will show the persistent UI at top level
```

### 4. **Early Return Pattern**
When all steps are done, show the transition UI and return early to prevent showing the run button.

## Key Changes Made

### File: `stages/stage7.py`

1. **Lines 271-341**: Added persistent Stage 8 transition UI at the top level
2. **Lines 502-523**: Simplified completion logic in button handler
3. **Line 341**: Added early return to prevent showing run button when done

## Expected Behavior After Fix

1. **User completes all test case steps** by running scripts in Stage 7
2. **On the final step**, when "Run Test Script" is clicked:
   - Test executes successfully
   - `state.all_steps_done = True` is set
   - Page reruns automatically
3. **Stage 7 UI now shows**:
   - ✅ "All test case steps have been processed!" message
   - Combined script display (if available)
   - **Two prominent buttons**:
     - "Proceed to Script Optimization (Phase 8)" (primary button)
     - "Return to Test Case Selection (Phase 3)" (alternative)
4. **User clicks "Proceed to Script Optimization"**:
   - Sets `st.session_state['transitioning_to_stage8'] = True`
   - App detects this flag and calls `stage8_optimize_script()`
   - User sees Stage 8 optimization interface

## Verification

The fix ensures that:
- ✅ Stage 8 transition UI is always visible when all steps are completed
- ✅ The transition happens outside the button click handler
- ✅ Users can reliably access Stage 8 after completing all steps
- ✅ The existing automatic advancement logic for non-final steps is preserved
- ✅ All logging and state management patterns are maintained

## Testing

To test the fix:
1. Complete all steps of a test case in Stage 7
2. Run the final step's script
3. Verify that the Stage 8 transition options appear persistently
4. Click "Proceed to Script Optimization (Phase 8)"
5. Verify that Stage 8 loads correctly

## Files Modified

- `stages/stage7.py` - Main fix implementation
- `test_stage7_stage8_transition.py` - Test script to verify the fix

## Impact

This fix resolves the critical workflow issue where users could not access Stage 8 script optimization after completing all test case steps, ensuring the complete workflow functions as designed.
