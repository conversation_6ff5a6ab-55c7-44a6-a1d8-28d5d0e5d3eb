#!/usr/bin/env python3
"""
Test script to verify the Stage 8 None value fix.

This script tests that Stage 8 can handle None values in state attributes
without throwing TypeError: object of type 'NoneType' has no len().
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stage8_none_handling():
    """Test that Stage 8 handles None values correctly."""
    print("🔍 Testing Stage 8 None value handling...")
    
    try:
        from state_manager import StateManager
        from stages.stage8 import stage8_optimize_script
        
        # Create a mock state with None values
        state = StateManager()
        
        # Set some attributes to None to simulate the error condition
        state.combined_script_content = None
        state.google_api_key = None
        state.combined_script_path = None
        state.selected_test_case = None
        
        print("✅ Created mock state with None values")
        print(f"   combined_script_content: {state.combined_script_content}")
        print(f"   google_api_key: {state.google_api_key}")
        print(f"   combined_script_path: {state.combined_script_path}")
        print(f"   selected_test_case: {state.selected_test_case}")
        
        # Test the debug logging section that was causing the error
        script_attrs = [
            'combined_script_content', 'combined_script_path', 'selected_test_case', 'google_api_key'
        ]
        
        print("\n🔍 Testing debug logging with None values...")
        for attr in script_attrs:
            value = getattr(state, attr, 'NOT_SET')
            print(f"   Testing {attr}: {value}")
            
            # This should not throw an error anymore
            if attr == 'combined_script_content' and value != 'NOT_SET' and value is not None:
                length = len(value)
                print(f"   ✅ {attr} length = {length} characters")
            elif attr == 'combined_script_content' and (value == 'NOT_SET' or value is None):
                print(f"   ✅ {attr} = None or not set")
            elif attr == 'google_api_key' and value != 'NOT_SET' and value is not None:
                length = len(value)
                print(f"   ✅ {attr} present = True (length: {length})")
            elif attr == 'google_api_key' and (value == 'NOT_SET' or value is None):
                print(f"   ✅ {attr} = None or not set")
            elif attr == 'selected_test_case' and value != 'NOT_SET' and value is not None:
                test_case_id = value.get('Test Case ID', 'Unknown') if isinstance(value, dict) else 'Invalid'
                print(f"   ✅ {attr} = {test_case_id}")
            else:
                print(f"   ✅ {attr} = {value}")
        
        print("\n✅ All debug logging tests passed - no TypeError!")
        return True
        
    except TypeError as e:
        if "object of type 'NoneType' has no len()" in str(e):
            print(f"❌ TypeError still occurs: {e}")
            return False
        else:
            print(f"❌ Unexpected TypeError: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prerequisites_checking():
    """Test the prerequisites checking function with None values."""
    print("\n🔍 Testing prerequisites checking with None values...")
    
    try:
        from state_manager import StateManager
        from stages.stage8 import _check_optimization_prerequisites
        
        # Create a mock state with None values
        state = StateManager()
        state.combined_script_content = None
        state.google_api_key = None
        state.combined_script_path = None
        state.selected_test_case = None
        
        # This should not throw an error
        prerequisites_met, missing_items = _check_optimization_prerequisites(state)
        
        print(f"✅ Prerequisites checking completed without error")
        print(f"   Prerequisites met: {prerequisites_met}")
        print(f"   Missing items: {missing_items}")
        
        # Should have missing items since everything is None
        expected_missing = ['google_api_key', 'combined_script_content', 'combined_script_path', 'selected_test_case']
        if set(missing_items) == set(expected_missing):
            print("✅ Correctly identified all missing items")
            return True
        else:
            print(f"❌ Missing items mismatch. Expected: {expected_missing}, Got: {missing_items}")
            return False
            
    except Exception as e:
        print(f"❌ Prerequisites checking failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_safe_len_operations():
    """Test various safe len() operations."""
    print("\n🔍 Testing safe len() operations...")
    
    test_cases = [
        (None, 0, "None value"),
        ("", 0, "Empty string"),
        ("test", 4, "Normal string"),
        ([], 0, "Empty list"),
        ([1, 2, 3], 3, "Normal list")
    ]
    
    for value, expected, description in test_cases:
        try:
            # Test the safe length calculation pattern used in the fix
            length = len(value) if value is not None else 0
            if length == expected:
                print(f"   ✅ {description}: {length}")
            else:
                print(f"   ❌ {description}: expected {expected}, got {length}")
                return False
        except Exception as e:
            print(f"   ❌ {description} failed: {e}")
            return False
    
    print("✅ All safe len() operations passed")
    return True

def main():
    """Run all tests to verify the Stage 8 None value fix."""
    print("🚀 TESTING STAGE 8 NONE VALUE FIX")
    print("=" * 60)
    
    tests = [
        test_stage8_none_handling,
        test_prerequisites_checking,
        test_safe_len_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The Stage 8 None value fix is working correctly.")
        print("\n📋 SUMMARY OF THE FIX:")
        print("   1. ✅ Added None checks before calling len() on state attributes")
        print("   2. ✅ Safe length calculation: len(value) if value is not None else 0")
        print("   3. ✅ Enhanced debug logging to handle None values gracefully")
        print("   4. ✅ Prerequisites checking now handles None values correctly")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    main()
